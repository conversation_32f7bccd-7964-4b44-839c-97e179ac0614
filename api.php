<?php
header('Content-Type: application/json');
require_once 'config.php';

$dataInicio = isset($_GET['data_inicio']) ? $_GET['data_inicio'] : '';
$dataFim = isset($_GET['data_fim']) ? $_GET['data_fim'] : '';
$horaInicio = isset($_GET['hora_inicio']) ? $_GET['hora_inicio'] : '';
$horaFim = isset($_GET['hora_fim']) ? $_GET['hora_fim'] : '';

$filtrosAplicados = !empty($dataInicio) && !empty($dataFim) && !empty($horaInicio) && !empty($horaFim);

function getAllData($pdo, $dataInicio = '', $dataFim = '', $horaInicio = '', $horaFim = '') {
    $data = [];
    $filtrosAplicados = !empty($dataInicio) && !empty($dataFim) && !empty($horaInicio) && !empty($horaFim);

    if ($filtrosAplicados) {
        $sql = "SELECT valor, datahora, escritorio FROM tb_tarefas_abertas
                WHERE DATE(datahora) BETWEEN ? AND ?
                AND TIME(datahora) BETWEEN ? AND ?
                ORDER BY datahora DESC LIMIT 1";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$dataInicio, $dataFim, $horaInicio, $horaFim]);
    } else {
        $sql = "SELECT valor, datahora, escritorio FROM tb_tarefas_abertas ORDER BY datahora DESC LIMIT 1";
        $stmt = $pdo->query($sql);
    }
    $tarefasAbertas = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($filtrosAplicados) {
        $sql = "SELECT valor, datahora, escritorio FROM tb_tarefas_concluidas
                WHERE DATE(datahora) BETWEEN ? AND ?
                AND TIME(datahora) BETWEEN ? AND ?
                ORDER BY datahora DESC LIMIT 1";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$dataInicio, $dataFim, $horaInicio, $horaFim]);
    } else {
        $sql = "SELECT valor, datahora, escritorio FROM tb_tarefas_concluidas ORDER BY datahora DESC LIMIT 1";
        $stmt = $pdo->query($sql);
    }
    $tarefasConcluidas = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($filtrosAplicados) {
        $sql = "SELECT valor, dataatual, escritorio FROM tb_metad
                WHERE dataatual BETWEEN ? AND ?
                ORDER BY dataatual DESC LIMIT 1";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$dataInicio, $dataFim]);
    } else {
        $sql = "SELECT valor, dataatual, escritorio FROM tb_metad ORDER BY dataatual DESC LIMIT 1";
        $stmt = $pdo->query($sql);
    }
    $metaDiaria = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($filtrosAplicados) {
        $sql = "SELECT chamados_pendentes, datahora FROM tb_onvio_messenger
                WHERE DATE(datahora) BETWEEN ? AND ?
                AND TIME(datahora) BETWEEN ? AND ?
                ORDER BY datahora DESC LIMIT 1";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$dataInicio, $dataFim, $horaInicio, $horaFim]);
    } else {
        $sql = "SELECT chamados_pendentes, datahora FROM tb_onvio_messenger ORDER BY datahora DESC LIMIT 1";
        $stmt = $pdo->query($sql);
    }
    $chamadosPendentes = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $valorTarefasAbertas = $tarefasAbertas ? $tarefasAbertas['valor'] : 0;
    $valorTarefasConcluidas = $tarefasConcluidas ? $tarefasConcluidas['valor'] : 0;
    $valorMeta = $metaDiaria ? $metaDiaria['valor'] : 0;
    $valorChamados = $chamadosPendentes ? $chamadosPendentes['chamados_pendentes'] : 0;
    
    $progressoMeta = $valorMeta > 0 ? min(100, ($valorTarefasConcluidas / $valorMeta) * 100) : 0;
    $tarefasRestantes = max(0, $valorMeta - $valorTarefasConcluidas);
    
    $data = [
        'tarefas_abertas' => $valorTarefasAbertas,
        'tarefas_concluidas' => $valorTarefasConcluidas,
        'meta_diaria' => $valorMeta,
        'chamados_pendentes' => $valorChamados,
        'progresso_meta' => round($progressoMeta, 1),
        'tarefas_restantes' => $tarefasRestantes,
        'ultima_atualizacao' => [
            'tarefas_abertas' => $tarefasAbertas ? $tarefasAbertas['datahora'] : null,
            'tarefas_concluidas' => $tarefasConcluidas ? $tarefasConcluidas['datahora'] : null,
            'chamados' => $chamadosPendentes ? $chamadosPendentes['datahora'] : null
        ],
        'filtros' => [
            'data_inicio' => $dataInicio,
            'data_fim' => $dataFim,
            'hora_inicio' => $horaInicio,
            'hora_fim' => $horaFim,
            'filtros_aplicados' => $filtrosAplicados
        ]
    ];
    
    return $data;
}

try {
    $data = getAllData($pdo, $dataInicio, $dataFim, $horaInicio, $horaFim);
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
} catch(Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Erro ao buscar dados: ' . $e->getMessage()]);
}
?>
