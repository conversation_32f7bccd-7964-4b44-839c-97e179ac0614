<?php
require_once 'config.php';
date_default_timezone_set("America/Sao_Paulo");

$dataInicio = isset($_GET['data_inicio']) ? $_GET['data_inicio'] : '';
$dataFim = isset($_GET['data_fim']) ? $_GET['data_fim'] : '';
$horaInicio = isset($_GET['hora_inicio']) ? $_GET['hora_inicio'] : '';
$horaFim = isset($_GET['hora_fim']) ? $_GET['hora_fim'] : '';
$escritorio = isset($_GET['escritorio']) ? $_GET['escritorio'] : '';
$departamento = isset($_GET['departamento']) ? $_GET['departamento'] : '';
/*$escritorio = 'M4';
$departamento = 'dp';*/

$filtrosAplicados = !empty($dataInicio) && !empty($dataFim) && !empty($horaInicio) && !empty($horaFim) || !empty($escritorio) || !empty($departamento);

function getTarefasAbertas($pdo, $dataInicio = '', $dataFim = '', $horaInicio = '', $horaFim = '', $escritorio = '', $departamento = '') {
    $conditions = [];
    $params = [];

    if (!empty($dataInicio) && !empty($dataFim)) {
        $conditions[] = "DATE(datahora) BETWEEN ? AND ?";
        $params[] = $dataInicio;
        $params[] = $dataFim;
    }

    if (!empty($horaInicio) && !empty($horaFim)) {
        $conditions[] = "TIME(datahora) BETWEEN ? AND ?";
        $params[] = $horaInicio;
        $params[] = $horaFim;
    }

    if (!empty($escritorio)) {
        $conditions[] = "escritorio = ?";
        $params[] = $escritorio;
    }

    if (!empty($departamento)) {
        $conditions[] = "departamento = ?";
        $params[] = $departamento;
    }

    $sql = "SELECT valor, datahora, escritorio, departamento FROM tb_tarefas_abertas";
    if (!empty($conditions)) {
        $sql .= " WHERE " . implode(" AND ", $conditions);
    }
    $sql .= " ORDER BY datahora DESC LIMIT 1";

    if (empty($params)) {
        $stmt = $pdo->query($sql);
    } else {
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
    }

    return $stmt->fetch(PDO::FETCH_ASSOC);
}

function getTarefasConcluidas($pdo, $dataInicio = '', $dataFim = '', $horaInicio = '', $horaFim = '', $escritorio = '', $departamento = '') {
    $conditions = [];
    $params = [];

    if (!empty($dataInicio) && !empty($dataFim)) {
        $conditions[] = "DATE(datahora) BETWEEN ? AND ?";
        $params[] = $dataInicio;
        $params[] = $dataFim;
    }

    if (!empty($horaInicio) && !empty($horaFim)) {
        $conditions[] = "TIME(datahora) BETWEEN ? AND ?";
        $params[] = $horaInicio;
        $params[] = $horaFim;
    }

    if (!empty($escritorio)) {
        $conditions[] = "escritorio = ?";
        $params[] = $escritorio;
    }

    if (!empty($departamento)) {
        $conditions[] = "departamento = ?";
        $params[] = $departamento;
    }

    $sql = "SELECT valor, datahora, escritorio, departamento FROM tb_tarefas_concluidas";
    if (!empty($conditions)) {
        $sql .= " WHERE " . implode(" AND ", $conditions);
    }
    $sql .= " ORDER BY datahora DESC LIMIT 1";

    if (empty($params)) {
        $stmt = $pdo->query($sql);
    } else {
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
    }

    return $stmt->fetch(PDO::FETCH_ASSOC);
}

function getMetaDiaria($pdo, $dataInicio = '', $dataFim = '', $escritorio = '', $departamento = '') {
    $conditions = [];
    $params = [];

    if (!empty($dataInicio) && !empty($dataFim)) {
        $conditions[] = "dataatual BETWEEN ? AND ?";
        $params[] = $dataInicio;
        $params[] = $dataFim;
    }

    if (!empty($escritorio)) {
        $conditions[] = "escritorio = ?";
        $params[] = $escritorio;
    }

    if (!empty($departamento)) {
        $conditions[] = "departamento = ?";
        $params[] = $departamento;
    }

    $sql = "SELECT valor, dataatual, escritorio, departamento FROM tb_metad";
    if (!empty($conditions)) {
        $sql .= " WHERE " . implode(" AND ", $conditions);
    }
    $sql .= " ORDER BY dataatual DESC LIMIT 1";

    if (empty($params)) {
        $stmt = $pdo->query($sql);
    } else {
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
    }

    return $stmt->fetch(PDO::FETCH_ASSOC);
}

function getChamadosPendentes($pdo, $dataInicio = '', $dataFim = '', $horaInicio = '', $horaFim = '') {
    $conditions = [];
    $params = [];

    if (!empty($dataInicio) && !empty($dataFim)) {
        $conditions[] = "DATE(datahora) BETWEEN ? AND ?";
        $params[] = $dataInicio;
        $params[] = $dataFim;
    }

    if (!empty($horaInicio) && !empty($horaFim)) {
        $conditions[] = "TIME(datahora) BETWEEN ? AND ?";
        $params[] = $horaInicio;
        $params[] = $horaFim;
    }

    $sql = "SELECT chamados_pendentes, datahora FROM tb_onvio_messenger";
    if (!empty($conditions)) {
        $sql .= " WHERE " . implode(" AND ", $conditions);
    }
    $sql .= " ORDER BY datahora DESC LIMIT 1";

    if (empty($params)) {
        $stmt = $pdo->query($sql);
    } else {
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
    }

    return $stmt->fetch(PDO::FETCH_ASSOC);
}

function calcularProgresso($concluidas, $meta) {
    if ($meta > 0) {
        return min(100, ($concluidas / $meta) * 100);
    }
    return 0;
}

function getEscritorios($pdo) {
    $sql = "SELECT DISTINCT escritorio FROM tb_metad WHERE escritorio IS NOT NULL AND escritorio != '' ORDER BY escritorio";
    $stmt = $pdo->query($sql);
    return $stmt->fetchAll(PDO::FETCH_COLUMN);
}

function getDepartamentos($pdo) {
    $sql = "SELECT DISTINCT departamento FROM tb_metad WHERE departamento IS NOT NULL AND departamento != '' ORDER BY departamento";
    $stmt = $pdo->query($sql);
    return $stmt->fetchAll(PDO::FETCH_COLUMN);
}

// Buscar valores únicos para os filtros
$escritorios = getEscritorios($pdo);
$departamentos = getDepartamentos($pdo);

$tarefasAbertas = getTarefasAbertas($pdo, $dataInicio, $dataFim, $horaInicio, $horaFim, $escritorio, $departamento);
$tarefasConcluidas = getTarefasConcluidas($pdo, $dataInicio, $dataFim, $horaInicio, $horaFim, $escritorio, $departamento);
$metaDiaria = getMetaDiaria($pdo, $dataInicio, $dataFim, $escritorio, $departamento);
$chamadosPendentes = getChamadosPendentes($pdo, $dataInicio, $dataFim, $horaInicio, $horaFim);

$valorTarefasAbertas = $tarefasAbertas ? $tarefasAbertas['valor'] : 0;
$valorTarefasConcluidas = $tarefasConcluidas ? $tarefasConcluidas['valor'] : 0;
$valorMeta = $metaDiaria ? $metaDiaria['valor'] : 0;
$valorChamados = $chamadosPendentes ? $chamadosPendentes['chamados_pendentes'] : 0;

$progressoMeta = calcularProgresso($valorTarefasConcluidas, $valorMeta);
$tarefasRestantes = max(0, $valorMeta - $valorTarefasConcluidas);
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MegaOffice - Gestão à Vista</title>
    <link rel="stylesheet" href="style.css">
    <link rel="icon" type="image/png" href="Imagens/icone.png">
    <link rel="shortcut icon" type="image/png" href="Imagens/icone.png">
    <link rel="apple-touch-icon" href="Imagens/Logo M4 - Grupos.png">
    <script src="https://cdn.fusioncharts.com/fusioncharts/latest/fusioncharts.js"></script>
    <script src="https://cdn.fusioncharts.com/fusioncharts/latest/themes/fusioncharts.theme.fusion.js"></script>
</head>
<body>
    <div class="container">
        <header>
            <div class="header-content">
                <img src="Imagens/Logo M4 - Grupos.png" alt="MegaOffice Logo" class="header-logo-main">
                <h1>GESTÃO À VISTA (BETA)</h1>
            </div>
            <div class="header-right">
                <button type="button" class="view-mode-btn" onclick="toggleViewMode()">
                    <span id="mode-icon">📺</span>
                    <span id="mode-text">Modo TV</span>
                </button>
                <div class="datetime">
                    <span id="current-datetime"></span>
                </div>
            </div>
        </header>

        <main class="dashboard">
            <div class="stats-section">
                <div class="section-header">
                    <h2>Indicadores Gerais</h2>
                    <button type="button" class="toggle-filters-btn" onclick="toggleFilters()">
                        <span id="filter-icon">⚙</span> Filtros
                    </button>
                </div>

                <div class="filters-container" id="filters-container" style="display: none;">
                <form method="GET" class="filters-form">
                    <div class="filter-group">
                        <label for="data_inicio">Data Início:</label>
                        <input type="date" id="data_inicio" name="data_inicio" value="<?php echo $dataInicio; ?>">
                    </div>

                    <div class="filter-group">
                        <label for="data_fim">Data Fim:</label>
                        <input type="date" id="data_fim" name="data_fim" value="<?php echo $dataFim; ?>">
                    </div>

                    <div class="filter-group">
                        <label for="hora_inicio">Hora Início:</label>
                        <input type="time" id="hora_inicio" name="hora_inicio" value="<?php echo $horaInicio; ?>">
                    </div>

                    <div class="filter-group">
                        <label for="hora_fim">Hora Fim:</label>
                        <input type="time" id="hora_fim" name="hora_fim" value="<?php echo $horaFim; ?>">
                    </div>

                    <div class="filter-group">
                        <label for="escritorio">Escritório:</label>
                        <select id="escritorio" name="escritorio">
                           
                            <?php foreach ($escritorios as $esc): ?>
                                <option value="<?php echo htmlspecialchars($esc); ?>" <?php echo $escritorio === $esc ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($esc); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="filter-group">
                        <label for="departamento">Departamento:</label>
                        <select id="departamento" name="departamento">
                            
                            <?php foreach ($departamentos as $dept): ?>
                                <option value="<?php echo htmlspecialchars($dept); ?>" <?php echo $departamento === $dept ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($dept); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="filter-group">
                        <button type="submit" class="filter-btn">Filtrar</button>
                       <!-- <button type="button" class="today-btn" onclick="filterToday()">Hoje</button>
                        <button type="button" class="reset-btn" onclick="resetFilters()">Últimos Dados</button>-->
                    </div>
                </form>
            </div>
            <div class="charts-section">
               <!-- <div class="section-header">
                    <h2>Indicadores Visuais</h2>
                 </div> -->

                <div class="charts-grid">
                    <div class="chart-card">
                        <h3>Progresso da Meta</h3>
                        <div class="chart-container">
                            <div id="metaChart"></div>
                        </div>
                        <div class="chart-info">
                            <span class="chart-value"><?php echo $valorTarefasConcluidas; ?>/<?php echo $valorMeta; ?></span>
                            <span class="chart-label"><?php echo number_format($progressoMeta, 1); ?>% Concluído</span>
                        </div>
                    </div>


                    <div class="chart-card">
                        <h3>Distribuição Geral</h3>
                        <div class="chart-container">
                            <div id="geralChart"></div>
                        </div>
                        <div class="chart-info">
                            <span class="chart-value"><?php echo $valorTarefasConcluidas + $valorTarefasAbertas + $valorChamados; ?></span>
                            <span class="chart-label">Total de Itens</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="cards-grid">
                

                <div class="card progress">
                    <div class="card-header">
                        <h3>Meta Diária</h3>
                    </div>
                    <div class="card-value"><?php echo $valorTarefasConcluidas; ?>/<?php echo $valorMeta; ?></div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: <?php echo $progressoMeta; ?>%"></div>
                    </div>
                    <div class="card-subtitle"><?php echo number_format($progressoMeta, 1); ?>% concluído</div>
                </div>

                <div class="card remaining">
                    <div class="card-header">
                        <h3>Tarefas Restantes</h3>
                    </div>
                    <div class="card-value"><?php echo $tarefasRestantes; ?></div>
                    <div class="card-subtitle">Para cumprir a meta</div>
                </div>

                <div class="card messenger">
                    <div class="card-header">
                        <h3>Chamados Pendentes</h3>
                    </div>
                    <div class="card-value"><?php echo $valorChamados; ?></div>
                    <div class="card-subtitle">Onvio Messenger</div>
                </div>
            </div>
            </div>

            <!-- Seção de Gráficos -->

            <!-- Informações adicionais -->
            <div class="info-grid">
                <div class="info-card">
                    <h4>Última Atualização - Tarefas Abertas</h4>
                    <p><?php echo $tarefasAbertas ? date('d/m/Y H:i', strtotime($tarefasAbertas['datahora'])) : 'Nenhum dado encontrado'; ?></p>
                </div>
                <div class="info-card">
                    <h4>Última Atualização - Tarefas Concluídas</h4>
                    <p><?php echo $tarefasConcluidas ? date('d/m/Y H:i', strtotime($tarefasConcluidas['datahora'])) : 'Nenhum dado encontrado'; ?></p>
                </div>
                <div class="info-card">
                    <h4>Última Atualização - Chamados</h4>
                    <p><?php echo $chamadosPendentes ? date('d/m/Y H:i', strtotime($chamadosPendentes['datahora'])) : 'Nenhum dado encontrado'; ?></p>
                </div>
                <div class="info-card">
                    <h4><?php echo $filtrosAplicados ? 'Filtros Aplicados' : 'Visualização'; ?></h4>
                    <p><?php
                        if ($filtrosAplicados) {
                            $filtros = [];
                            if (!empty($dataInicio) && !empty($dataFim)) {
                                $filtros[] = date('d/m/Y', strtotime($dataInicio)) . ' até ' . date('d/m/Y', strtotime($dataFim));
                            }
                            if (!empty($horaInicio) && !empty($horaFim)) {
                                $filtros[] = $horaInicio . ' às ' . $horaFim;
                            }
                            if (!empty($escritorio)) {
                                $filtros[] = 'Escritório: ' . $escritorio;
                            }
                            if (!empty($departamento)) {
                                $filtros[] = 'Departamento: ' . $departamento;
                            }
                            echo implode('<br>', $filtros);
                        } else {
                            echo 'Últimos dados coletados';
                        }
                    ?></p>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Dados do PHP para JavaScript
        window.dashboardData = {
            tarefasConcluidas: <?php echo $valorTarefasConcluidas; ?>,
            tarefasAbertas: <?php echo $valorTarefasAbertas; ?>,
            metaDiaria: <?php echo $valorMeta; ?>,
            chamadosPendentes: <?php echo $valorChamados; ?>,
            progressoMeta: <?php echo $progressoMeta; ?>
        };

        // Função para alternar entre modo Desktop e TV
        function toggleViewMode() {
            const body = document.body;
            const modeIcon = document.getElementById('mode-icon');
            const modeText = document.getElementById('mode-text');

            if (body.classList.contains('tv-mode')) {
                // Mudar para modo Desktop
                body.classList.remove('tv-mode');
                body.classList.add('desktop-mode');
                modeIcon.textContent = '💻';
                modeText.textContent = 'Modo Desktop';
                localStorage.setItem('viewMode', 'desktop');
            } else {
                // Mudar para modo TV
                body.classList.remove('desktop-mode');
                body.classList.add('tv-mode');
                modeIcon.textContent = '📺';
                modeText.textContent = 'Modo TV';
                localStorage.setItem('viewMode', 'tv');
            }

            // Recriar gráficos com novas configurações de fonte
            setTimeout(() => {
                if (typeof initFusionCharts === 'function') {
                    initFusionCharts();
                }
            }, 100);
        }

        // Carregar modo salvo ao inicializar
        function loadSavedViewMode() {
            const savedMode = localStorage.getItem('viewMode') || 'desktop';
            const body = document.body;
            const modeIcon = document.getElementById('mode-icon');
            const modeText = document.getElementById('mode-text');

            if (savedMode === 'tv') {
                body.classList.add('tv-mode');
                modeIcon.textContent = '💻';
                modeText.textContent = 'Modo Desktop';
            } else {
                body.classList.add('desktop-mode');
                modeIcon.textContent = '📺';
                modeText.textContent = 'Modo TV';
            }
        }

        // Carregar modo salvo quando a página inicializar
        document.addEventListener('DOMContentLoaded', function() {
            loadSavedViewMode();
        });
    </script>
    <script src="script.js"></script>
</body>
</html>
