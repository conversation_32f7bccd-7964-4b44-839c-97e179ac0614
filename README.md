# Dashboard - Gestão à Vista

Dashboard para visualização de dados de gestão empresarial em tempo real.

## Funcionalidades

- **Visualização Padrão**: <PERSON><PERSON> padr<PERSON>, mostra os últimos dados coletados do banco (mais recentes)
- **Tarefas em Atraso/Vencendo Hoje**: Mostra quantas tarefas estão em atraso ou vencendo
- **Meta Diária**: Exibe o progresso das tarefas concluídas em relação à meta estabelecida
- **Tarefas Restantes**: Calcula quantas tarefas ainda precisam ser concluídas para atingir a meta
- **Chamados Pendentes**: Mostra a quantidade de chamados pendentes no Onvio Messenger
- **Filtros de Data e Hora**: Permite filtrar os dados por período específico (data início/fim e hora início/fim)
- **Botões de Ação**: "Filtrar", "Hoje" (dados do dia atual) e "Últimos Dados" (volta ao padrão)
- **Validação de Filtros**: Validação automática para garantir que as datas e horários sejam consistentes
- **Atualização Automática**: A página se atualiza automaticamente a cada 5 minutos

## Estrutura dos Arquivos

- `dashboard.php` - Página principal da dashboard
- `config.php` - Configurações de conexão com o banco de dados
- `api.php` - API para buscar dados em formato JSON
- `style.css` - Estilos CSS da dashboard
- `script.js` - Scripts JavaScript para interatividade

## Configuração do Banco de Dados

O sistema utiliza as seguintes tabelas:

### tb_tarefas_abertas
- `id` - Identificador único
- `valor` - Número de tarefas abertas
- `datahora` - Data e hora do registro
- `escritorio` - Nome do escritório

### tb_tarefas_concluidas
- `id` - Identificador único
- `valor` - Número de tarefas concluídas
- `datahora` - Data e hora do registro
- `escritorio` - Nome do escritório

### tb_metad
- `id` - Identificador único
- `valor` - Meta diária de tarefas
- `dataatual` - Data da meta
- `escritorio` - Nome do escritório

### tb_onvio_messenger
- `id` - Identificador único
- `chamados_pendentes` - Número de chamados pendentes
- `datahora` - Data e hora do registro

## Instalação

1. Faça upload dos arquivos para seu servidor web
2. Certifique-se de que o PHP está configurado corretamente
3. Verifique se a extensão PDO MySQL está habilitada
4. Acesse `dashboard.php` no seu navegador

## Configurações

As configurações de banco de dados estão no arquivo `config.php`:

```php
$host = 'srv725.hstgr.io';
$dbname = 'u327687948_gestaoavista';
$username = 'u327687948_megaofficedb';
$password = 'Crss170900#Gremio#1966';
```

## Recursos Visuais

- Design responsivo que se adapta a diferentes tamanhos de tela
- Cores diferenciadas para cada tipo de informação
- Animações suaves e efeitos visuais
- Interface de filtros intuitiva e moderna
- Indicadores visuais para tarefas urgentes
- Validação visual dos formulários

## Tecnologias Utilizadas

- **PHP** - Backend e conexão com banco de dados
- **HTML5** - Estrutura da página
- **CSS3** - Estilos e animações
- **JavaScript** - Interatividade e gráficos

- **MySQL** - Banco de dados

## Funcionalidades Avançadas

- Auto-refresh da página a cada 5 minutos
- Formatação automática de números grandes
- Indicadores visuais para tarefas em atraso
- Data e hora atualizadas em tempo real
- API REST para integração com outros sistemas
- Filtros avançados por data e horário (sem considerar segundos)
- Botão de reset para voltar aos filtros padrão
- Validação de formulários em tempo real

## Como Usar

**Visualização Padrão:**
- Ao abrir a dashboard, você verá automaticamente os últimos dados coletados do banco

**Filtros Disponíveis:**
1. **Selecione as datas**: Escolha data início e fim
2. **Defina os horários**: Selecione hora início e fim (sem segundos)
3. **Clique em "Filtrar"**: Os dados serão atualizados para o período selecionado

**Botões de Ação:**
- **"Filtrar"**: Aplica os filtros selecionados
- **"Hoje"**: Mostra dados apenas do dia atual (00:00 às 23:59)
- **"Últimos Dados"**: Volta para a visualização padrão (últimos dados coletados)

## Suporte

Para suporte ou dúvidas sobre o sistema, consulte a documentação ou entre em contato com a equipe de desenvolvimento.
