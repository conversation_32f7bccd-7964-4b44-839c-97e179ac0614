* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 50%, #f1f3f4 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    min-height: 100vh;
    color: #2c3e50;
    font-size: 24px;
    position: relative;
    overflow-x: hidden;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('Imagens/Envelope MegaOffice - sem fundo.png') no-repeat center center;
    background-size: 200px;
    opacity: 0.03;
    z-index: -1;
    pointer-events: none;
}

.container {
    max-width: 100%;
    margin: 0;
    padding: 40px;
    min-height: 100vh;
    box-sizing: border-box;
}

header {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    padding: 60px 80px;
    border-radius: 24px;
    margin-bottom: 60px;
    box-shadow: 0 12px 48px rgba(44, 62, 80, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    overflow: hidden;
}

header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #e67e22 0%, #f39c12 50%, #e67e22 100%);
}

.header-content {
    display: flex;
    align-items: center;
    gap: 40px;
}

.header-logo-main {
    width: 300px;
    height: auto;
    filter: brightness(0) invert(1);
    transition: all 0.3s ease;
}

.header-logo-main:hover {
    transform: scale(1.05);
    filter: brightness(0) invert(1) drop-shadow(0 0 10px rgba(255, 255, 255, 0.3));
}

header h1 {
    color: #ffffff;
    font-size: 3.5rem;
    font-weight: 700;
    margin: 0;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    letter-spacing: 2px;
    text-transform: uppercase;
}

.datetime {
    font-size: 2rem;
    color: #bdc3c7;
    font-weight: 600;
    background: rgba(255, 255, 255, 0.1);
    padding: 20px 40px;
    border-radius: 30px;
    backdrop-filter: blur(10px);
    letter-spacing: 1px;
}

.dashboard {
    display: grid;
    gap: 60px;
}

/* Seção de Estatísticas */
.stats-section {
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(44, 62, 80, 0.08);
    border: 1px solid rgba(230, 126, 34, 0.1);
    overflow: hidden;
    position: relative;
}

.stats-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #e67e22 0%, #f39c12 100%);
}

.section-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    padding: 50px 80px;
    border-bottom: 2px solid rgba(230, 126, 34, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.section-header h2 {
    color: #2c3e50;
    font-size: 3rem;
    font-weight: 700;
    margin: 0;
    position: relative;
}

.section-header h2::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 80px;
    height: 6px;
    background: linear-gradient(90deg, #e67e22 0%, #f39c12 100%);
    border-radius: 3px;
}

.toggle-filters-btn {
    background: #e67e22;
    color: white;
    border: none;
    padding: 24px 48px;
    border-radius: 16px;
    font-size: 1.8rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 16px;
    box-shadow: 0 4px 16px rgba(230, 126, 34, 0.2);
    text-transform: none;
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
}

.toggle-filters-btn:hover {
    background: #d35400;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(230, 126, 34, 0.3);
}

.toggle-filters-btn.active {
    background: #27ae60;
    box-shadow: 0 2px 8px rgba(39, 174, 96, 0.2);
}

.toggle-filters-btn.active:hover {
    background: #229954;
    box-shadow: 0 4px 12px rgba(39, 174, 96, 0.3);
}

.toggle-filters-btn.has-filters {
    background: #2c3e50;
    box-shadow: 0 2px 8px rgba(44, 62, 80, 0.2);
}

.toggle-filters-btn.has-filters:hover {
    background: #34495e;
    box-shadow: 0 4px 12px rgba(44, 62, 80, 0.3);
}

.filter-badge {
    color: #f39c12;
    font-size: 0.75rem;
    margin-left: 4px;
}

.filters-container {
    padding: 24px 32px;
    border-top: 1px solid rgba(230, 126, 34, 0.1);
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    transition: all 0.3s ease;
    overflow: hidden;
}

.filters-container.show {
    animation: slideDown 0.3s ease-out;
}

.filters-container.hide {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        max-height: 0;
        padding-top: 0;
        padding-bottom: 0;
    }
    to {
        opacity: 1;
        max-height: 200px;
        padding-top: 24px;
        padding-bottom: 24px;
    }
}

@keyframes slideUp {
    from {
        opacity: 1;
        max-height: 200px;
        padding-top: 24px;
        padding-bottom: 24px;
    }
    to {
        opacity: 0;
        max-height: 0;
        padding-top: 0;
        padding-bottom: 0;
    }
}

.filters-form {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.filter-group label {
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.875rem;
    margin-bottom: 6px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.filter-group input {
    padding: 12px 16px;
    border: 2px solid #ecf0f1;
    border-radius: 10px;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    background: #ffffff;
    color: #2c3e50;
    font-weight: 500;
}

.filter-group input:focus {
    outline: none;
    border-color: #e67e22;
    box-shadow: 0 0 0 3px rgba(230, 126, 34, 0.1);
    transform: translateY(-1px);
}

.filter-group select {
    padding: 12px 16px;
    border: 2px solid #ecf0f1;
    border-radius: 10px;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    background: #ffffff;
    color: #2c3e50;
    cursor: pointer;
    min-width: 150px;
    font-weight: 500;
}

.filter-group select:focus {
    outline: none;
    border-color: #e67e22;
    box-shadow: 0 0 0 3px rgba(230, 126, 34, 0.1);
    transform: translateY(-1px);
}

.filter-group select option {
    padding: 8px 12px;
    background: #ffffff;
    color: #2c3e50;
}

.filter-group:last-child {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
}

.filter-btn, .reset-btn, .today-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    margin: 0;
    text-transform: none;
    letter-spacing: 0.3px;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    gap: 8px;
    width: 200px;
    justify-content: center;
}

.filter-btn {
    background: #e67e22;
    color: white;
    box-shadow: 0 2px 8px rgba(230, 126, 34, 0.2);
}

.filter-btn:hover {
    background: #d35400;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(230, 126, 34, 0.3);
}

.today-btn {
    background: #27ae60;
    color: white;
    box-shadow: 0 2px 8px rgba(39, 174, 96, 0.2);
}

.today-btn:hover {
    background: #229954;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(39, 174, 96, 0.3);
}

.reset-btn {
    background: #2c3e50;
    color: white;
    box-shadow: 0 2px 8px rgba(44, 62, 80, 0.2);
}

.reset-btn:hover {
    background: #34495e;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(44, 62, 80, 0.3);
}

/* Adicionar ícone de seta em todos os botões */
.filter-btn::after,
.today-btn::after,
.reset-btn::after {
    content: '→';
    margin-left: 4px;
    font-size: 1rem;
    transition: transform 0.2s ease;
}

.filter-btn:hover::after,
.today-btn:hover::after,
.reset-btn:hover::after {
    transform: translateX(2px);
}

.cards-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 60px;
    margin-bottom: 60px;
}

.card {
    background: #ffffff;
    padding: 60px;
    border-radius: 32px;
    box-shadow: 0 12px 48px rgba(44, 62, 80, 0.08);
    border: 2px solid rgba(230, 126, 34, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    min-height: 400px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(44, 62, 80, 0.15);
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, #e67e22 0%, #f39c12 100%);
}

.card.urgent::before {
    background: linear-gradient(90deg, #e74c3c 0%, #c0392b 100%);
}

.card.progress::before {
    background: linear-gradient(90deg, #27ae60 0%, #2ecc71 100%);
}

.card.remaining::before {
    background: linear-gradient(90deg, #f39c12 0%, #e67e22 100%);
}

.card.messenger::before {
    background: linear-gradient(90deg, #9b59b6 0%, #8e44ad 100%);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.card-header h3 {
    font-size: 2rem;
    color: #2c3e50;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 2px;
    position: relative;
    margin-bottom: 20px;
}

.card-header h3::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 30px;
    height: 2px;
    background: linear-gradient(90deg, #e67e22 0%, #f39c12 100%);
    border-radius: 1px;
}

.icon {
    font-size: 1.5rem;
    opacity: 0.7;
    color: #e67e22;
}

.card-value {
    font-size: 6rem;
    font-weight: 800;
    color: #2c3e50;
    margin-bottom: 24px;
    line-height: 1;
    text-shadow: 0 4px 8px rgba(44, 62, 80, 0.1);
    text-align: center;
}

.card-subtitle {
    color: #7f8c8d;
    font-size: 1.6rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    text-align: center;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #ecf0f1;
    border-radius: 20px;
    margin: 32px 0;
    overflow: hidden;
    box-shadow: inset 0 4px 8px rgba(0, 0, 0, 0.1);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #27ae60 0%, #2ecc71 100%);
    border-radius: 10px;
    transition: width 0.5s ease;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}



/* Seção de Gráficos */
.charts-section {
    background: #ffffff;
    border-radius: 20px;
    box-shadow: 0 12px 40px rgba(44, 62, 80, 0.1);
    border: 1px solid rgba(230, 126, 34, 0.1);
    overflow: hidden;
    margin-bottom: 24px;
    position: relative;
}

.charts-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, #e67e22 0%, #f39c12 50%, #e67e22 100%);
}

.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
    gap: 32px;
    padding: 40px;
}

.chart-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 20px;
    padding: 36px;
    text-align: center;
    border: 1px solid rgba(230, 126, 34, 0.1);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(44, 62, 80, 0.08);
    min-height: 480px;
}

.chart-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 16px 48px rgba(44, 62, 80, 0.15);
}

.chart-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #e67e22 0%, #f39c12 25%, #27ae60 50%, #2c3e50 75%, #9b59b6 100%);
}

.chart-card h3 {
    color: #2c3e50;
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 28px;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
}

.chart-card h3::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background: linear-gradient(90deg, #e67e22 0%, #f39c12 100%);
    border-radius: 2px;
}

.chart-container {
    position: relative;
    height: 320px;
    margin-bottom: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 15px;
    padding: 20px;
}

.chart-container > div {
    width: 100%;
    height: 100%;
}

.chart-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    margin-top: 20px;
    padding: 16px;
    background: rgba(230, 126, 34, 0.05);
    border-radius: 15px;
    border: 1px solid rgba(230, 126, 34, 0.1);
}

.chart-value {
    font-size: 2rem;
    font-weight: 800;
    color: #2c3e50;
    text-shadow: 0 2px 4px rgba(44, 62, 80, 0.1);
}

.chart-label {
    font-size: 0.9rem;
    color: #7f8c8d;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 600;
}

.chart-card {
    animation: fadeInUp 0.8s ease-out;
}

.chart-card:nth-child(1) { animation-delay: 0.2s; }
.chart-card:nth-child(2) { animation-delay: 0.4s; }
.chart-card:nth-child(3) { animation-delay: 0.6s; }

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(40px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 60px;
    margin-top: 60px;
}

.info-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    padding: 60px;
    border-radius: 24px;
    box-shadow: 0 12px 48px rgba(44, 62, 80, 0.08);
    border: 2px solid rgba(230, 126, 34, 0.1);
    position: relative;
    transition: all 0.3s ease;
    min-height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.info-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 40px rgba(44, 62, 80, 0.12);
}

.info-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #e67e22 0%, #f39c12 100%);
    border-radius: 16px 16px 0 0;
}

.info-card h4 {
    color: #2c3e50;
    margin-bottom: 24px;
    font-size: 2rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 2px;
    position: relative;
}

.info-card h4::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 30px;
    height: 2px;
    background: linear-gradient(90deg, #e67e22 0%, #f39c12 100%);
    border-radius: 1px;
}

.info-card p {
    color: #7f8c8d;
    font-size: 1.6rem;
    font-weight: 500;
    line-height: 1.6;
}


@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    header {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .header-content {
        flex-direction: column;
        gap: 15px;
    }

    .header-logo-main {
        width: 100px;
    }

    header h1 {
        font-size: 1.2rem;
    }

    .section-header {
        flex-direction: column;
        gap: 12px;
        text-align: center;
    }

    .filters-form {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .filter-btn, .reset-btn, .today-btn {
        width: 100%;
        margin: 0 0 8px 0;
    }

    .filter-group:last-child {
        align-items: stretch;
    }

    .charts-grid {
        grid-template-columns: 1fr;
        gap: 16px;
        padding: 16px;
    }

    .chart-container {
        height: 180px;
    }

    .cards-grid {
        grid-template-columns: 1fr;
    }

    .card {
        padding: 20px;
    }

    .card-value {
        font-size: 2.5rem;
    }
}

/* Animações */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card {
    animation: fadeIn 0.6s ease-out;
}

.card:nth-child(1) { animation-delay: 0.1s; }
.card:nth-child(2) { animation-delay: 0.2s; }
.card:nth-child(3) { animation-delay: 0.3s; }
.card:nth-child(4) { animation-delay: 0.4s; }

.urgent .card-value {
    color: #e74c3c;
    text-shadow: 0 2px 4px rgba(231, 76, 60, 0.2);
}

.progress .card-value {
    color: #27ae60;
    text-shadow: 0 2px 4px rgba(39, 174, 96, 0.2);
}

.remaining .card-value {
    color: #f39c12;
    text-shadow: 0 2px 4px rgba(243, 156, 18, 0.2);
}

.messenger .card-value {
    color: #9b59b6;
    text-shadow: 0 2px 4px rgba(155, 89, 182, 0.2);
}

/* Efeitos adicionais para melhorar a experiência visual */
.card {
    backdrop-filter: blur(10px);
}

.card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent 0%, rgba(230, 126, 34, 0.02) 100%);
    pointer-events: none;
}

/* Animação de pulso para valores importantes */
.card-value {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.02); }
}





/* Efeito de loading para os cards */
.card.loading {
    position: relative;
    overflow: hidden;
}

.card.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(230, 126, 34, 0.1), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Responsividade melhorada para dispositivos móveis */
@media (max-width: 480px) {
    .container {
        padding: 10px;
    }

    header {
        padding: 16px 20px;
        border-radius: 12px;
    }

    header h1 {
        font-size: 1.4rem;
    }

    .card {
        padding: 20px;
        border-radius: 16px;
    }

    .card-value {
        font-size: 2rem;
    }

    .charts-grid {
        padding: 20px;
        gap: 20px;
    }

    .chart-card {
        padding: 24px;
        min-height: 400px;
    }
}

/* Efeito de hover melhorado para os cards */
.card:hover .card-value {
    transform: scale(1.05);
    transition: transform 0.3s ease;
}

.card:hover .card-header h3::after {
    width: 50px;
    transition: width 0.3s ease;
}

/* Estilo para indicadores de status */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
    animation: blink 2s infinite;
}

.status-indicator.online {
    background: #27ae60;
}

.status-indicator.warning {
    background: #f39c12;
}

.status-indicator.offline {
    background: #e74c3c;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}

/* Otimizações específicas para TV 4K */
@media screen and (min-width: 3840px) {
    body {
        font-size: 32px;
    }

    .container {
        padding: 80px;
    }

    header {
        padding: 100px 120px;
    }

    header h1 {
        font-size: 5rem;
    }

    .header-logo-main {
        width: 400px;
    }

    .datetime {
        font-size: 2.5rem;
        padding: 30px 60px;
    }

    .section-header h2 {
        font-size: 4rem;
    }

    .card-value {
        font-size: 8rem;
    }

    .card-header h3 {
        font-size: 2.5rem;
    }

    .card-subtitle {
        font-size: 2rem;
    }

    .info-card h4 {
        font-size: 2.5rem;
    }

    .info-card p {
        font-size: 2rem;
    }
}

/* Remover scroll horizontal */
html, body {
    overflow-x: hidden;
    max-width: 100vw;
}
