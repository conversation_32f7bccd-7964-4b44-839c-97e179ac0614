function updateDateTime() {
    const now = new Date();
    const options = {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    };
    
    const dateTimeString = now.toLocaleDateString('pt-BR', options);
    document.getElementById('current-datetime').textContent = dateTimeString;
}

setInterval(updateDateTime, 1000);
updateDateTime();

// Função para mostrar/ocultar filtros
function toggleFilters() {
    const filtersContainer = document.getElementById('filters-container');
    const toggleBtn = document.querySelector('.toggle-filters-btn');
    const filterIcon = document.getElementById('filter-icon');

    if (filtersContainer.style.display === 'none' || filtersContainer.style.display === '') {
        // Mostrar filtros
        filtersContainer.style.display = 'block';
        filtersContainer.classList.remove('hide');
        filtersContainer.classList.add('show');
        toggleBtn.classList.add('active');
        filterIcon.textContent = '▲';
    } else {
        // Ocultar filtros com animação
        filtersContainer.classList.remove('show');
        filtersContainer.classList.add('hide');
        toggleBtn.classList.remove('active');
        filterIcon.textContent = '⚙';

        // Ocultar completamente após a animação
        setTimeout(() => {
            filtersContainer.style.display = 'none';
            filtersContainer.classList.remove('hide');
        }, 300);
    }
}

function resetFilters() {
    document.getElementById('data_inicio').value = '';
    document.getElementById('data_fim').value = '';
    document.getElementById('hora_inicio').value = '';
    document.getElementById('hora_fim').value = '';
    document.getElementById('escritorio').value = '';
    document.getElementById('departamento').value = '';

    document.querySelector('.filters-form').submit();
}

function filterToday() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('data_inicio').value = today;
    document.getElementById('data_fim').value = today;
    document.getElementById('hora_inicio').value = '00:00';
    document.getElementById('hora_fim').value = '23:59';
    // Manter os valores de escritório e departamento selecionados

    document.querySelector('.filters-form').submit();
}

// Verificar se há filtros ativos ao carregar a página
function checkActiveFilters() {
    const dataInicio = document.getElementById('data_inicio');
    const dataFim = document.getElementById('data_fim');
    const horaInicio = document.getElementById('hora_inicio');
    const horaFim = document.getElementById('hora_fim');
    const toggleBtn = document.querySelector('.toggle-filters-btn');

    const hasFilters = dataInicio.value || dataFim.value || horaInicio.value || horaFim.value;

    if (hasFilters) {
        toggleBtn.classList.add('has-filters');
        toggleBtn.innerHTML = '<span id="filter-icon">⚙</span> Filtros <span class="filter-badge">●</span>';
    } else {
        toggleBtn.classList.remove('has-filters');
        toggleBtn.innerHTML = '<span id="filter-icon">⚙</span> Filtros';
    }
}

// Função para inicializar gráficos FusionCharts
function initFusionCharts() {
    // Verificar se FusionCharts está carregado
    if (typeof FusionCharts === 'undefined') {
        console.error('FusionCharts não foi carregado');
        return;
    }

    // Verificar se os dados estão disponíveis
    if (!window.dashboardData) {
        console.error('Dados da dashboard não encontrados');
        return;
    }

    const data = window.dashboardData;
    const tarefasConcluidas = data.tarefasConcluidas || 0;
    const tarefasAbertas = data.tarefasAbertas || 0;
    const metaDiaria = data.metaDiaria || 0;
    const chamadosPendentes = data.chamadosPendentes || 0;
    const tarefasRestantes = Math.max(0, metaDiaria - tarefasConcluidas);

    // Gráfico 1: Progresso da Meta (Doughnut)
    const metaChart = new FusionCharts({
        type: 'doughnut2d',
        renderAt: 'metaChart',
        width: '100%',
        height: '300',
        dataFormat: 'json',
        dataSource: {
            chart: {
                caption: '',
                theme: 'fusion',
                showPercentValues: '1',
                showPercentInTooltip: '1',
                enableMultiSlicing: '0',
                showLegend: '1',
                legendPosition: 'bottom',
                use3DLighting: '0',
                showShadow: '0',
                paletteColors: '#059669,#e5e7eb',
                bgColor: '#ffffff',
                showBorder: '0',
                legendBgColor: '#ffffff',
                legendBorderThickness: '0',
                legendShadow: '0',
                doughnutRadius: '60',
                pieRadius: '80'
            },
            data: [
                {
                    label: 'Concluídas',
                    value: tarefasConcluidas,
                    color: '#059669'
                },
                {
                    label: 'Restantes',
                    value: Math.max(1, tarefasRestantes),
                    color: '#e5e7eb'
                }
            ]
        }
    });
    metaChart.render();

    // Gráfico 2: Status das Tarefas (Pie)
    const tarefasChart = new FusionCharts({
        type: 'pie2d',
        renderAt: 'tarefasChart',
        width: '100%',
        height: '300',
        dataFormat: 'json',
        dataSource: {
            chart: {
                caption: '',
                theme: 'fusion',
                showPercentValues: '1',
                showPercentInTooltip: '1',
                enableMultiSlicing: '0',
                showLegend: '1',
                legendPosition: 'bottom',
                use3DLighting: '0',
                showShadow: '0',
                paletteColors: '#059669,#dc2626',
                bgColor: '#ffffff',
                showBorder: '0',
                legendBgColor: '#ffffff',
                legendBorderThickness: '0',
                legendShadow: '0',
                pieRadius: '80'
            },
            data: [
                {
                    label: 'Concluídas',
                    value: Math.max(1, tarefasConcluidas),
                    color: '#059669'
                },
                {
                    label: 'Em Atraso',
                    value: Math.max(1, tarefasAbertas),
                    color: '#dc2626'
                }
            ]
        }
    });
    tarefasChart.render();

    // Gráfico 3: Distribuição Geral (Column)
    const geralChart = new FusionCharts({
        type: 'column2d',
        renderAt: 'geralChart',
        width: '100%',
        height: '300',
        dataFormat: 'json',
        dataSource: {
            chart: {
                caption: '',
                theme: 'fusion',
                showValues: '1',
                showLegend: '0',
                use3DLighting: '0',
                showShadow: '0',
                paletteColors: '#059669,#dc2626,#7c3aed',
                bgColor: '#ffffff',
                showBorder: '0',
                yAxisName: 'Quantidade',
                xAxisName: 'Categorias',
                plotSpacePercent: '60',
                canvasBgColor: '#ffffff',
                canvasBorderThickness: '0'
            },
            data: [
                {
                    label: 'Concluídas',
                    value: tarefasConcluidas,
                    color: '#059669'
                },
                {
                    label: 'Pendentes',
                    value: tarefasRestantes,
                    color: '#d97706'
                },
                {
                    label: 'Chamados',
                    value: chamadosPendentes,
                    color: '#7c3aed'
                }
                
            ]
        }
    });
    geralChart.render();

    console.log('Gráficos FusionCharts inicializados com sucesso!');
}

document.addEventListener('DOMContentLoaded', function() {
    // Inicializar gráficos FusionCharts
    setTimeout(() => {
        initFusionCharts();
    }, 500);

    // Verificar filtros ativos
    checkActiveFilters();

    const form = document.querySelector('.filters-form');
    const dataInicio = document.getElementById('data_inicio');
    const dataFim = document.getElementById('data_fim');
    const horaInicio = document.getElementById('hora_inicio');
    const horaFim = document.getElementById('hora_fim');

    form.addEventListener('submit', function(e) {
       if (dataInicio.value && dataFim.value && horaInicio.value && horaFim.value) {
            if (dataInicio.value > dataFim.value) {
                e.preventDefault();
                alert('A data de início não pode ser maior que a data fim!');
                return;
            }

           if (dataInicio.value === dataFim.value && horaInicio.value > horaFim.value) {
                e.preventDefault();
                alert('A hora de início não pode ser maior que a hora fim!');
                return;
            }
        }
    });
});

setInterval(function() {
    location.reload();
}, 300000); 

function checkUrgentTasks() {
    const urgentValue = document.querySelector('.card.urgent .card-value').textContent;
    if (parseInt(urgentValue) > 0) {
       
        document.querySelector('.card.urgent').classList.add('alert-blink');
    }
}


const style = document.createElement('style');
style.textContent = `
    .alert-blink {
        animation: blink 2s infinite;
    }
    
    @keyframes blink {
        0%, 50% { opacity: 1; }
        25%, 75% { opacity: 0.7; }
    }
`;
document.head.appendChild(style);


checkUrgentTasks();


document.querySelectorAll('.card').forEach(card => {
    card.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-8px) scale(1.02)';
    });
    
    card.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0) scale(1)';
    });
});

function formatNumber(num) {
    if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'k';
    }
    return num.toString();
}


document.addEventListener('DOMContentLoaded', function() {
    const cardValues = document.querySelectorAll('.card-value');
    cardValues.forEach(element => {
        const value = parseInt(element.textContent);
        if (value >= 1000) {
            element.textContent = formatNumber(value);
        }
    });
});

// Função para alternar entre modo Desktop e TV
function toggleViewMode() {
    const body = document.body;
    const modeIcon = document.getElementById('mode-icon');
    const modeText = document.getElementById('mode-text');

    if (body.classList.contains('tv-mode')) {
        // Mudar para modo Desktop
        body.classList.remove('tv-mode');
        body.classList.add('desktop-mode');
        modeIcon.textContent = '💻';
        modeText.textContent = 'Modo Desktop';
        localStorage.setItem('viewMode', 'desktop');
    } else {
        // Mudar para modo TV
        body.classList.remove('desktop-mode');
        body.classList.add('tv-mode');
        modeIcon.textContent = '📺';
        modeText.textContent = 'Modo TV';
        localStorage.setItem('viewMode', 'tv');
    }
}

// Carregar modo salvo ao inicializar
function loadSavedViewMode() {
    const savedMode = localStorage.getItem('viewMode') || 'desktop';
    const body = document.body;
    const modeIcon = document.getElementById('mode-icon');
    const modeText = document.getElementById('mode-text');

    if (savedMode === 'tv') {
        body.classList.add('tv-mode');
        modeIcon.textContent = '💻';
        modeText.textContent = 'Modo Desktop';
    } else {
        body.classList.add('desktop-mode');
        modeIcon.textContent = '📺';
        modeText.textContent = 'Modo TV';
    }
}

// Carregar modo salvo quando a página inicializar
document.addEventListener('DOMContentLoaded', function() {
    loadSavedViewMode();
});

console.log('Dashboard carregado com sucesso!');
